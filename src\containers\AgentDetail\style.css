.agent-detail {
  min-height: 100vh;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.detail-container {
  max-width: 1000px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24px;
  padding: 3rem;
  box-shadow: 0 32px 64px rgba(0, 0, 0, 0.1);
}

.agent-intro {
  display: flex;
  gap: 2rem;
  margin-bottom: 3rem;
  align-items: flex-start;
}

.agent-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-icon {
  font-size: 5rem;
  display: block;
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  padding: 1rem;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.agent-level-badge {
  position: absolute;
  top: -10px;
  right: -10px;
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

.agent-info h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.agent-specialty {
  color: #667eea;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.agent-description {
  color: #6b7280;
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.agent-experience {
  color: #9ca3af;
  font-style: italic;
}

.agent-details {
  margin-bottom: 3rem;
}

.detail-section {
  margin-bottom: 2.5rem;
}

.detail-section h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.skills-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.skill-tag {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 500;
}

.companies-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.company-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  border: 1px solid rgba(102, 126, 234, 0.2);
  font-weight: 500;
}

.questions-preview {
  background: #f9fafb;
  border-radius: 16px;
  padding: 1.5rem;
}

.question-preview {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: flex-start;
}

.question-number {
  background: #667eea;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.question-content {
  flex: 1;
}

.question-type-tag {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.2rem 0.6rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: inline-block;
}

.question-content p {
  color: #374151;
  margin: 0;
  line-height: 1.5;
}

.more-questions {
  color: #9ca3af;
  font-style: italic;
  text-align: center;
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
}

.action-section {
  display: flex;
  gap: 1rem;
  justify-content: center;
  align-items: center;
}

.start-interview-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.start-interview-btn:hover {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.back-btn {
  color: #6b7280;
  text-decoration: none;
  padding: 1rem 2rem;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.back-btn:hover {
  color: #374151;
  border-color: #d1d5db;
  background: #f9fafb;
}

/* 面试会话样式 */
.interview-session {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.interview-header {
  max-width: 1000px;
  margin: 0 auto 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 1.5rem;
}

.interview-progress {
  flex: 1;
  max-width: 300px;
}

.interview-progress span {
  color: white;
  font-weight: 600;
  font-size: 0.9rem;
  display: block;
  margin-bottom: 0.5rem;
}

.progress-bar {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
}

.progress-fill {
  background: linear-gradient(45deg, #4facfe, #00f2fe);
  height: 100%;
  border-radius: 10px;
  transition: width 0.3s ease;
}

.exit-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.exit-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.interview-content {
  max-width: 1000px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: calc(100vh - 200px);
}

.ai-interviewer {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

.ai-avatar {
  font-size: 4rem;
  text-align: center;
  margin-bottom: 1rem;
}

.ai-message h3 {
  color: #1f2937;
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
}

.question-type {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.question-text {
  color: #374151;
  font-size: 1.1rem;
  line-height: 1.6;
  flex: 1;
}

.answer-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

.answer-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.record-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.record-btn.recording {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.or-text {
  color: #9ca3af;
  font-weight: 500;
}

.text-option {
  color: #667eea;
  font-weight: 600;
}

.answer-input {
  flex: 1;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  padding: 1rem;
  font-size: 1rem;
  resize: none;
  margin-bottom: 1.5rem;
}

.answer-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.answer-actions {
  display: flex;
  gap: 1rem;
}

.submit-answer-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.submit-answer-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, #5a6fd8, #6a4190);
  transform: translateY(-2px);
}

.submit-answer-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.skip-btn {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid #e5e7eb;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.skip-btn:hover {
  background: rgba(107, 114, 128, 0.2);
  color: #374151;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
  color: white;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .agent-intro {
    flex-direction: column;
    text-align: center;
  }
  
  .interview-content {
    grid-template-columns: 1fr;
    height: auto;
  }
  
  .interview-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .action-section {
    flex-direction: column;
  }
}