import { useState } from 'react'
import './style.css'

const agents = [
  {
    id: 1,
    name: '计算机科学面试官',
    specialty: '计算机科学',
    description: '专业的计算机科学技术面试，涵盖算法、数据结构、系统设计等',
    icon: '💻',
    level: '高级',
    features: ['算法解析', '代码评估', '系统设计', '技术深度']
  },
  {
    id: 2,
    name: '产品经理面试官',
    specialty: '产品管理',
    description: '产品思维、用户体验、商业分析等产品经理核心能力评估',
    icon: '📱',
    level: '中级',
    features: ['产品思维', '用户分析', '商业洞察', '沟通能力']
  },
  {
    id: 3,
    name: '市场营销面试官',
    specialty: '市场营销',
    description: '市场策略、品牌建设、数据分析等营销专业技能考核',
    icon: '📊',
    level: '中级',
    features: ['策略思维', '创意能力', '数据分析', '市场洞察']
  },
  {
    id: 4,
    name: '金融分析面试官',
    specialty: '金融分析',
    description: '财务建模、投资分析、风险评估等金融专业能力测试',
    icon: '💰',
    level: '高级',
    features: ['财务分析', '投资建模', '风险评估', '市场分析']
  },
  {
    id: 5,
    name: '人力资源面试官',
    specialty: '人力资源',
    description: '组织行为、员工发展、绩效管理等HR核心技能评估',
    icon: '👥',
    level: '中级',
    features: ['组织管理', '人才发展', '沟通协调', '政策理解']
  },
  {
    id: 6,
    name: '数据科学面试官',
    specialty: '数据科学',
    description: '机器学习、统计分析、数据可视化等数据科学技能考核',
    icon: '📈',
    level: '高级',
    features: ['机器学习', '统计分析', '数据处理', '业务理解']
  }
]

const Home = () => {
  const [selectedCategory, setSelectedCategory] = useState('全部')
  
  const categories = ['全部', '技术类', '管理类', '分析类']
  
  const filteredAgents = agents.filter(agent => {
    if (selectedCategory === '全部') return true
    if (selectedCategory === '技术类') return ['计算机科学', '数据科学'].includes(agent.specialty)
    if (selectedCategory === '管理类') return ['产品管理', '人力资源'].includes(agent.specialty)
    if (selectedCategory === '分析类') return ['市场营销', '金融分析'].includes(agent.specialty)
    return true
  })

  return (
    <div className="home">
      <section className="hero-section">
        <div className="hero-content">
          <h1>AI驱动的智能面试评测平台</h1>
          <p>多模态智能分析，专业面试官AI，为你的求职之路保驾护航</p>
          <div className="hero-stats">
            <div className="stat">
              <span className="stat-number">10,000+</span>
              <span className="stat-label">成功面试</span>
            </div>
            <div className="stat">
              <span className="stat-number">50+</span>
              <span className="stat-label">专业领域</span>
            </div>
            <div className="stat">
              <span className="stat-number">95%</span>
              <span className="stat-label">用户满意度</span>
            </div>
          </div>
        </div>
        <div className="hero-visual">
          <div className="floating-card">
            <div className="ai-avatar">🤖</div>
            <div className="ai-text">
              <div className="typing-animation">正在分析您的回答...</div>
            </div>
          </div>
        </div>
      </section>

      <section className="agents-section">
        <div className="section-header">
          <h2>选择您的专业面试官</h2>
          <p>根据您的专业背景，选择最适合的AI面试官</p>
        </div>
        
        <div className="category-filter">
          {categories.map(category => (
            <button
              key={category}
              className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="agents-grid">
          {filteredAgents.map(agent => (
            <div key={agent.id} className="agent-card">
              <div className="agent-header">
                <div className="agent-icon">{agent.icon}</div>
                <div className="agent-level">{agent.level}</div>
              </div>
              <h3>{agent.name}</h3>
              <p className="agent-specialty">{agent.specialty}</p>
              <p className="agent-description">{agent.description}</p>
              <div className="agent-features">
                {agent.features.map(feature => (
                  <span key={feature} className="feature-tag">{feature}</span>
                ))}
              </div>
              <button 
                className="start-interview-btn"
                onClick={() => window.location.href = `/agent/${agent.id}`}
              >
                开始面试
              </button>
            </div>
          ))}
        </div>
      </section>

      <section className="features-section">
        <h2>为什么选择我们？</h2>
        <div className="features-grid">
          <div className="feature-item">
            <div className="feature-icon">🎯</div>
            <h3>精准评估</h3>
            <p>AI多维度分析，精准评估面试表现</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">🔄</div>
            <h3>实时反馈</h3>
            <p>即时获得详细反馈和改进建议</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">📊</div>
            <h3>数据驱动</h3>
            <p>基于大数据的专业面试评估模型</p>
          </div>
          <div className="feature-item">
            <div className="feature-icon">🌟</div>
            <h3>个性化</h3>
            <p>根据不同专业定制面试内容和评估标准</p>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home
