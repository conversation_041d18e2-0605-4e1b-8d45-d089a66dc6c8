import { NavLink } from 'react-router-dom'
import './style.css'

const Navigation = ({ isLoggedIn }) => {
  return (
    <nav className="navigation">
      <ul className="nav-list">
        <li>
          <NavLink to="/" className="nav-link">
            首页
          </NavLink>
        </li> 
        {isLoggedIn ? (
          <>
            <li>
              <NavLink to="/dashboard" className="nav-link">
                我的面试
              </NavLink>
            </li>
            <li>
              <button className="nav-link logout-btn">
                退出登录
              </button>
            </li>
          </>
        ) : (
          <>
            <li>
              <NavLink to="/login" className="nav-link">
                登录
              </NavLink>
            </li>
            <li>
              <NavLink to="/register" className="nav-link register-btn">
                注册
              </NavLink>
            </li>
          </>
        )}
      </ul>
    </nav>
  )
}

export default Navigation
