import { useState } from 'react'
import { Link } from 'react-router-dom'
import './style.css'

const Login = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('登录数据:', formData)
    // 这里将来会接入实际的登录API
    alert('登录功能开发中...')
  }

  return (
    <div className="auth-page">
      <div className="auth-container">
        <div className="auth-header">
          <h1>欢迎回来</h1>
          <p>登录您的智慧面试账户</p>
        </div>
        
        <form className="auth-form" onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="email">邮箱地址</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              placeholder="请输入您的邮箱"
              required
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">密码</label>
            <div className="password-input">
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleChange}
                placeholder="请输入您的密码"
                required
              />
              <button
                type="button"
                className="password-toggle"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? '🙈' : '👁️'}
              </button>
            </div>
          </div>
          
          <div className="form-options">
            <label className="checkbox-label">
              <input type="checkbox" />
              <span>记住我</span>
            </label>
            <Link to="/forgot-password" className="forgot-link">
              忘记密码？
            </Link>
          </div>
          
          <button type="submit" className="auth-btn">
            登录
          </button>
        </form>
        
        <div className="auth-divider">
          <span>或</span>
        </div>
        
        <div className="social-login">
          <button className="social-btn google">
            <span>🔍</span>
            使用 Google 登录
          </button>
          <button className="social-btn github">
            <span>📱</span>
            使用微信登录
          </button>
        </div>
        
        <div className="auth-footer">
          <p>
            还没有账户？
            <Link to="/register" className="auth-link">
              立即注册
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}

export default Login
