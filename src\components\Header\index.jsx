import { useState } from 'react'
import Navigation from '../Navigation'
import './style.css'

const Header = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false)

  return (
    <header className="header">
      <div className="header-container">
        <div className="logo">
          <h1>智慧面试</h1>
          <span>AI Interview System</span>
        </div>
        <Navigation isLoggedIn={isLoggedIn} />
      </div>
    </header>
  )
}

export default Header