.navigation {
  display: flex;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 1rem;
  align-items: center;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  border: none;
  background: none;
  cursor: pointer;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.nav-link.active {
  background-color: rgba(255, 255, 255, 0.2);
}

.register-btn {
  background: linear-gradient(45deg, #ff6b6b, #ff8e53);
  color: white !important;
}

.register-btn:hover {
  background: linear-gradient(45deg, #ff5252, #ff7043);
  transform: translateY(-2px);
}

.logout-btn {
  font-size: 0.9rem;
}