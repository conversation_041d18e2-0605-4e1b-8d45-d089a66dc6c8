import { useState, useEffect } from 'react'
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom'
import './style.css'

const AgentDetail = () => {
  const { id } = useParams()
  const [agent, setAgent] = useState(null)
  const [isInterviewStarted, setIsInterviewStarted] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState(0)
  const [userAnswer, setUserAnswer] = useState('')
  const [isRecording, setIsRecording] = useState(false)

  // 模拟数据
  const agentData = {
    1: {
      id: 1,
      name: '计算机科学面试官',
      specialty: '计算机科学',
      description: '专业的计算机科学技术面试，涵盖算法、数据结构、系统设计等',
      icon: '💻',
      level: '高级',
      experience: '具有10年以上软件开发和技术面试经验',
      features: ['算法解析', '代码评估', '系统设计', '技术深度'],
      companies: ['Google', 'Microsoft', '阿里巴巴', '腾讯'],
      questions: [
        {
          id: 1,
          type: 'algorithm',
          question: '请解释一下快速排序算法的工作原理，并分析其时间复杂度。',
          expectedPoints: ['分治思想', '选择基准元素', '分区操作', '递归调用', '时间复杂度分析']
        },
        {
          id: 2,
          type: 'system_design',
          question: '如何设计一个支持百万用户的聊天系统？请描述主要的技术架构。',
          expectedPoints: ['负载均衡', '数据库设计', '消息队列', '缓存策略', '可扩展性']
        },
        {
          id: 3,
          type: 'coding',
          question: '请实现一个LRU缓存，支持get和put操作。',
          expectedPoints: ['哈希表', '双向链表', '时间复杂度O(1)', '空间优化']
        }
      ]
    },
    // 可以添加更多智能体数据
  }

  useEffect(() => {
    const agentInfo = agentData[id]
    setAgent(agentInfo)
  }, [id])

  const startInterview = () => {
    setIsInterviewStarted(true)
    setCurrentQuestion(0)
  }

  const nextQuestion = () => {
    if (currentQuestion < agent.questions.length - 1) {
      setCurrentQuestion(currentQuestion + 1)
      setUserAnswer('')
    }
  }

  const toggleRecording = () => {
    setIsRecording(!isRecording)
    // 这里将来会接入实际的录音功能
  }

  const submitAnswer = () => {
    console.log('用户答案:', userAnswer)
    // 这里将来会接入AI评估功能
    alert('答案已提交，AI正在评估中...')
    nextQuestion()
  }

  if (!agent) {
    return (
      <div className="agent-detail">
        <div className="loading">
          <div className="loading-spinner"></div>
          <p>加载中...</p>
        </div>
      </div>
    )
  }

  if (isInterviewStarted) {
    const question = agent.questions[currentQuestion]
    
    return (
      <div className="interview-session">
        <div className="interview-header">
          <div className="interview-progress">
            <span>问题 {currentQuestion + 1} / {agent.questions.length}</span>
            <div className="progress-bar">
              <div 
                className="progress-fill" 
                style={{width: `${((currentQuestion + 1) / agent.questions.length) * 100}%`}}
              ></div>
            </div>
          </div>
          <button className="exit-btn" onClick={() => setIsInterviewStarted(false)}>
            退出面试
          </button>
        </div>
        
        <div className="interview-content">
          <div className="ai-interviewer">
            <div className="ai-avatar">{agent.icon}</div>
            <div className="ai-message">
              <h3>{agent.name}</h3>
              <div className="question-type">{question.type}</div>
              <p className="question-text">{question.question}</p>
            </div>
          </div>
          
          <div className="answer-section">
            <div className="answer-controls">
              <button 
                className={`record-btn ${isRecording ? 'recording' : ''}`}
                onClick={toggleRecording}
              >
                {isRecording ? '🛑 停止录音' : '🎤 开始录音'}
              </button>
              <span className="or-text">或</span>
              <span className="text-option">文字回答</span>
            </div>
            
            <textarea
              className="answer-input"
              value={userAnswer}
              onChange={(e) => setUserAnswer(e.target.value)}
              placeholder="请在这里输入您的答案..."
              rows="8"
            />
            
            <div className="answer-actions">
              <button 
                className="submit-answer-btn"
                onClick={submitAnswer}
                disabled={!userAnswer.trim() && !isRecording}
              >
                提交答案
              </button>
              {currentQuestion < agent.questions.length - 1 && (
                <button className="skip-btn" onClick={nextQuestion}>
                  跳过此题
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="agent-detail">
      <div className="detail-container">
        <div className="agent-intro">
          <div className="agent-avatar">
            <span className="avatar-icon">{agent.icon}</span>
            <div className="agent-level-badge">{agent.level}</div>
          </div>
          
          <div className="agent-info">
            <h1>{agent.name}</h1>
            <p className="agent-specialty">{agent.specialty}</p>
            <p className="agent-description">{agent.description}</p>
            <p className="agent-experience">{agent.experience}</p>
          </div>
        </div>
        
        <div className="agent-details">
          <div className="detail-section">
            <h3>专业技能</h3>
            <div className="skills-grid">
              {agent.features.map(feature => (
                <span key={feature} className="skill-tag">{feature}</span>
              ))}
            </div>
          </div>
          
          <div className="detail-section">
            <h3>合作企业</h3>
            <div className="companies-list">
              {agent.companies.map(company => (
                <span key={company} className="company-tag">{company}</span>
              ))}
            </div>
          </div>
          
          <div className="detail-section">
            <h3>面试内容预览</h3>
            <div className="questions-preview">
              {agent.questions.slice(0, 2).map((question, index) => (
                <div key={question.id} className="question-preview">
                  <div className="question-number">Q{index + 1}</div>
                  <div className="question-content">
                    <span className="question-type-tag">{question.type}</span>
                    <p>{question.question}</p>
                  </div>
                </div>
              ))}
              <div className="more-questions">
                +{agent.questions.length - 2} 更多问题...
              </div>
            </div>
          </div>
        </div>
        
        <div className="action-section">
          <button className="start-interview-btn" onClick={startInterview}>
            开始面试
          </button>
          <Link to="/" className="back-btn">
            返回首页
          </Link>
        </div>
      </div>
    </div>
  )
}

export default AgentDetail